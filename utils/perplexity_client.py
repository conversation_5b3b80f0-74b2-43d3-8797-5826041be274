"""
Perplexity AI Sonar Client
-------------------------
Client for interacting with Perplexity AI Sonar API for in-depth research report generation.
"""

import os
import json
import logging
import requests
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class PerplexityClient:
    """
    Client for Perplexity AI Sonar API.
    
    This client handles communication with Perplexity AI's Sonar model for generating
    comprehensive research reports based on detailed queries.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Perplexity client.
        
        Args:
            api_key (Optional[str]): Perplexity API key. If not provided, will use PERPLEXITY_API_KEY from environment.
        """
        self.api_key = api_key or os.getenv("PERPLEXITY_API_KEY")
        if not self.api_key:
            raise ValueError("Perplexity API key is required. Set PERPLEXITY_API_KEY environment variable or pass api_key parameter.")
        
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        logger.info("Perplexity client initialized")
    
    def generate_research_report(self, 
                               query: str, 
                               model: str = "sonar-deep-research",
                               reasoning_effort: str = "low") -> Dict[str, Any]:
        """
        Generate a comprehensive research report using Perplexity AI Sonar.
        
        Args:
            query (str): The detailed research query to investigate
            model (str): The Perplexity model to use. Defaults to "sonar-deep-research"
            reasoning_effort (str): The reasoning effort level. Defaults to "low"
            
        Returns:
            Dict[str, Any]: The complete response from Perplexity API including the research report
            
        Raises:
            requests.RequestException: If the API request fails
            ValueError: If the response is invalid
        """
        payload = {
            "model": model,
            "reasoning_effort": reasoning_effort,
            "messages": [
                {
                    "role": "user", 
                    "content": f"Provide a in-depth report on the below query\n\n{query}"
                }
            ]
        }
        
        logger.info(f"Generating research report with model: {model}")
        logger.debug(f"Query length: {len(query)} characters")
        
        try:
            response = requests.post(self.base_url, json=payload, headers=self.headers)
            response.raise_for_status()
            
            result = response.json()
            
            # Validate response structure
            if "choices" not in result or not result["choices"]:
                raise ValueError("Invalid response structure from Perplexity API")
            
            if "message" not in result["choices"][0] or "content" not in result["choices"][0]["message"]:
                raise ValueError("Invalid message structure in Perplexity API response")
            
            logger.info("Research report generated successfully")
            logger.info(f"Usage: {result.get('usage', {})}")
            
            return result
            
        except requests.RequestException as e:
            logger.error(f"Error calling Perplexity API: {str(e)}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing Perplexity API response: {str(e)}")
            raise ValueError(f"Invalid JSON response from Perplexity API: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in Perplexity API call: {str(e)}")
            raise
    
    def extract_report_content(self, api_response: Dict[str, Any]) -> str:
        """
        Extract the main report content from Perplexity API response.
        
        The content after the </think> block contains the actual research report.
        
        Args:
            api_response (Dict[str, Any]): The complete API response from generate_research_report
            
        Returns:
            str: The extracted report content
        """
        try:
            content = api_response["choices"][0]["message"]["content"]
            
            # Find the end of the thinking block
            think_end = content.find("</think>")
            if think_end != -1:
                # Extract content after </think> block
                report_content = content[think_end + len("</think>"):].strip()
                logger.info("Extracted report content after </think> block")
                return report_content
            else:
                # If no </think> block found, return the entire content
                logger.warning("No </think> block found, returning entire content")
                return content.strip()
                
        except (KeyError, IndexError) as e:
            logger.error(f"Error extracting report content: {str(e)}")
            raise ValueError(f"Invalid API response structure: {str(e)}")
    
    def save_report(self, api_response: Dict[str, Any], file_path: str) -> None:
        """
        Save the complete API response to a JSON file.
        
        Args:
            api_response (Dict[str, Any]): The complete API response
            file_path (str): Path where to save the report JSON file
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(api_response, f, indent=4, ensure_ascii=False)
            
            logger.info(f"Research report saved to: {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving report to {file_path}: {str(e)}")
            raise
