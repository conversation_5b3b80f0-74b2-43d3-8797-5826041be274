"""
DataForSEO Client
----------------
Client for interacting with DataForSEO API for Google Trends keyword analysis.
"""

import os
import json
import base64
import logging
import requests
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class DataForSEOClient:
    """
    Client for DataForSEO API.
    
    This client handles communication with DataForSEO's Google Trends API for analyzing
    keyword trends and popularity data.
    """
    
    def __init__(self, api_key: Optional[str] = None, api_login: Optional[str] = None):
        """
        Initialize the DataForSEO client.
        
        Args:
            api_key (Optional[str]): DataForSEO API key. If not provided, will use DATAFORSEO_API_KEY from environment.
        """
        self.api_key = api_key or os.getenv("DATAFORSEO_API_KEY")
        self.api_login = api_login or os.getenv("DATAFORSEO_API_LOGIN")
        if not self.api_key:
            raise ValueError("DataForSEO API key is required. Set DATAFORSEO_API_KEY environment variable or pass api_key parameter.")
        
        self.base_url = "https://api.dataforseo.com/v3/keywords_data/google_trends/explore/live"
        
        # Encode credentials for basic auth
        encoded_credentials = base64.b64encode(f"{self.api_login}:{self.api_key}".encode('utf-8')).decode('utf-8')
        self.headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        }
        
        logger.info("DataForSEO client initialized")
    
    def get_keyword_trends(self, 
                          keywords: List[str],
                          time_range: str = "past_7_days",
                          location_code: int = 2356,  # India
                          language_code: str = "en",
                          category_code: int = 0,
                          search_type: str = "youtube") -> Dict[str, Any]:
        """
        Get Google Trends data for specified keywords.
        
        Args:
            keywords (List[str]): List of keywords to analyze
            time_range (str): Time range for trends analysis. Defaults to "past_7_days"
            location_code (int): Location code for trends analysis. Defaults to 2356 (India)
            language_code (str): Language code. Defaults to "en"
            category_code (int): Category code. Defaults to 0 (all categories)
            search_type (str): Type of search. Defaults to "youtube"
            
        Returns:
            Dict[str, Any]: The complete response from DataForSEO API
            
        Raises:
            requests.RequestException: If the API request fails
            ValueError: If the response is invalid
        """
        payload = json.dumps([{
            "time_range": time_range,
            "category_code": category_code,
            "type": search_type,
            "keywords": keywords,
            "location_code": location_code,
            "language_code": language_code
        }])
        
        logger.info(f"Analyzing trends for {len(keywords)} keywords")
        logger.debug(f"Keywords: {keywords}")
        
        try:
            response = requests.post(self.base_url, headers=self.headers, data=payload)
            response.raise_for_status()
            
            result = response.json()
            
            # Validate response structure
            if "tasks" not in result or not result["tasks"]:
                raise ValueError("Invalid response structure from DataForSEO API")
            
            if result["status_code"] != 20000:
                raise ValueError(f"DataForSEO API error: {result.get('status_message', 'Unknown error')}")
            
            logger.info("Keyword trends analysis completed successfully")
            logger.info(f"Cost: ${result.get('cost', 0)}")
            
            return result
            
        except requests.RequestException as e:
            logger.error(f"Error calling DataForSEO API: {str(e)}")
            raise

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing DataForSEO API response: {str(e)}")
            raise ValueError(f"Invalid JSON response from DataForSEO API: {str(e)}")

        except Exception as e:
            logger.error(f"Unexpected error in DataForSEO API call: {str(e)}")
            raise

    def extract_trending_keywords(self, 
                                api_response: Dict[str, Any], 
                                min_trend_value: int = 10,
                                max_keywords: int = 20) -> List[Dict[str, Any]]:
        """
        Extract trending keywords from DataForSEO API response.
        
        Args:
            api_response (Dict[str, Any]): The complete API response from get_keyword_trends
            min_trend_value (int): Minimum trend value to consider a keyword as trending. Defaults to 10
            max_keywords (int): Maximum number of keywords to return. Defaults to 20
            
        Returns:
            List[Dict[str, Any]]: List of trending keywords with their trend data
        """
        try:
            trending_keywords = []
            
            # Navigate through the response structure
            tasks = api_response.get("tasks", [])
            if not tasks:
                logger.warning("No tasks found in API response")
                return trending_keywords
            
            task = tasks[0]
            result = task.get("result", [])
            if not result:
                logger.warning("No result found in API response")
                return trending_keywords
            
            items = result[0].get("items", [])
            if not items:
                logger.warning("No items found in API response")
                return trending_keywords
            
            # Extract trend data
            trend_item = items[0]
            keywords = trend_item.get("keywords", [])
            trend_data = trend_item.get("data", [])
            
            # Calculate average trend values for each keyword
            for i, keyword in enumerate(keywords):
                values = []
                for data_point in trend_data:
                    if data_point.get("values") and len(data_point["values"]) > i:
                        value = data_point["values"][i]
                        if value is not None:
                            values.append(value)
                
                if values:
                    avg_trend = sum(values) / len(values)
                    max_trend = max(values)
                    
                    if avg_trend >= min_trend_value:
                        trending_keywords.append({
                            "keyword": keyword,
                            "average_trend": round(avg_trend, 2),
                            "max_trend": max_trend,
                            "data_points": len(values)
                        })
            
            # Sort by average trend value (descending) and limit results
            trending_keywords.sort(key=lambda x: x["average_trend"], reverse=True)
            trending_keywords = trending_keywords[:max_keywords]
            
            logger.info(f"Extracted {len(trending_keywords)} trending keywords")
            
            return trending_keywords
            
        except (KeyError, IndexError, TypeError) as e:
            logger.error(f"Error extracting trending keywords: {str(e)}")
            return []
    
    def save_trends_data(self, api_response: Dict[str, Any], file_path: str) -> None:
        """
        Save the complete API response to a JSON file.
        
        Args:
            api_response (Dict[str, Any]): The complete API response
            file_path (str): Path where to save the trends data JSON file
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(api_response, f, indent=4, ensure_ascii=False)
            
            logger.info(f"Trends data saved to: {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving trends data to {file_path}: {str(e)}")
            raise
