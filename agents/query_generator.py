"""
Query Generation Agent
---------------------
Generates detailed research queries for Perplexity AI Sonar based on story title and context.
"""

import logging

from crewai import Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent

logger = logging.getLogger(__name__)


class QueryGenerationAgent:
    """
    Agent for generating detailed research queries for Perplexity AI Sonar.
    
    This agent takes a story title and optional context to create comprehensive
    research queries that will be used by Perplexity AI Sonar to generate
    in-depth research reports.
    """
    
    def __init__(self, 
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai") -> None:
        """
        Initialize the Query Generation Agent.
        
        Args:
            verbose (bool): Whether to enable verbose output from CrewAI. Defaults to False.
            model (str): The model to use for the agent. Defaults to "gpt-4o-mini".
            provider (str): The LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        
        logger.info(f"QueryGenerationAgent initialized with model: {model}, provider: {provider}")
    
    def generate_research_query(self, title: str, context: str = "") -> str:
        """
        Generate a detailed research query for Perplexity AI Sonar.
        
        Args:
            title (str): The title of the story/incident to research
            context (str): Optional additional context about the story/incident
            
        Returns:
            str: A detailed research query optimized for Perplexity AI Sonar
        """
        logger.info(f"Generating research query for title: {title}")
        
        # Create the query generation agent
        query_agent = create_rate_limited_agent(
            role="Research Query Specialist",
            goal="Generate comprehensive and detailed research queries for in-depth investigation of real incidents and events",
            backstory="""You are an expert research query specialist with extensive experience in 
            investigative journalism and academic research. Your expertise lies in crafting detailed, 
            multi-faceted research queries that ensure comprehensive coverage of real incidents and events.
            
            You understand how to structure queries to gather:
            - Historical context and background information
            - Key events and timeline details
            - Multiple perspectives and viewpoints
            - Factual verification and source credibility
            - Cultural and social implications
            - Long-term impacts and consequences
            
            Your queries are designed to work optimally with AI research tools like Perplexity AI Sonar
            to generate thorough, factual, and well-sourced research reports.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )
        
        # Create the research query generation task
        query_task = Task(
            description=f"""Generate a comprehensive research query for investigating the following real incident/event:

Title: {title}
{f"Additional Context: {context}" if context else ""}

Create a detailed research query that will be used with Perplexity AI Sonar to generate an in-depth research report. 
The query should be structured to gather comprehensive information about this real incident/event.

Your query should include multiple research angles and specific questions that cover:

1. **Historical Context and Background**
   - What led to this incident/event?
   - What was the historical and social context?
   - Who were the key people/organizations involved?

2. **Detailed Event Analysis**
   - What exactly happened? (chronological sequence)
   - Where and when did it occur?
   - What were the immediate circumstances?

3. **Multiple Perspectives**
   - Different viewpoints from various stakeholders
   - Official statements vs. witness accounts
   - Media coverage and public reaction

4. **Verification and Sources**
   - Primary sources and documentation
   - Official records and reports
   - Credible news sources and investigations

5. **Impact and Consequences**
   - Immediate aftermath and responses
   - Long-term implications and changes
   - Lessons learned or reforms implemented

6. **Cultural and Social Context**
   - Relevant cultural, political, or social factors
   - How this incident reflects broader issues
   - Public discourse and debate generated

Format your query as a comprehensive research brief that will guide Perplexity AI Sonar to conduct thorough research. 
Make it detailed enough to ensure factual accuracy and comprehensive coverage, but focused enough to maintain coherence.

The query should be optimized for generating a research report that can later be used to create a compelling 
Hindi documentary-style narration.""",
            agent=query_agent,
            expected_output="A comprehensive, detailed research query optimized for Perplexity AI Sonar that will generate an in-depth, factual research report about the specified real incident/event."
        )
        
        # Execute the task
        crew = Crew(
            agents=[query_agent],
            tasks=[query_task],
            process=Process.sequential,
            verbose=self.verbose
        )
        
        try:
            result = crew.kickoff()
            
            # Extract the query from the result
            if hasattr(result, 'raw'):
                query = result.raw
            else:
                query = str(result)
            
            logger.info("Research query generated successfully")
            logger.debug(f"Generated query length: {len(query)} characters")
            
            return query.strip()

        except Exception as e:
            logger.error(f"Error generating research query: {str(e)}")
            raise
